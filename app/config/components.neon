	services:
		#FORM/COMPONENT FACTORIES
		- \VisualPaginator

		-
		    implement: App\Admin\Components\ISignInFormFactory
		    inject: true

		-
		    implement: App\Admin\Components\ILostPasswordFormFactory
		    inject: true

		-
		    implement: App\Admin\Components\IRestPasswordFormFactory
		    inject: true

		-
		    implement: App\FrontModule\Components\ContactForm\IContactFormFactory
		    inject: true

		-
		    implement: App\FrontModule\Components\LotteryForm\ILotteryFormFactory
		    inject: true

		-
			implement: \SuperKoderi\Components\IBreedingFormFactory
			inject: true

		-
		    implement: \SuperKoderi\Components\IPosteRestanteFormFactory
		    inject: true

		-
		    implement: \SuperKoderi\Components\IConfigFormFactory
		-
			implement: \SuperKoderi\Components\IServicesFormFactory
		-
			implement: \SuperKoderi\Components\IFlagsFormFactory
		-
			implement: \SuperKoderi\Components\IAgreementFormFactory
		-
			implement: \SuperKoderi\Components\ISynonymsFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\INewsletterFormFactory
			inject: true
		-
			implement: SuperKoderi\Components\IRedirectMultiFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\ISignInFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IProfileFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRegistrationFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRegistrationAnimalFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRegistrationAgreementFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IAnimalEditFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\ILostPasswordFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IMessageForFormFactory
		-
		    implement: \SuperKoderi\Components\IUserEditFormFactory
		    inject: true
		-
			implement: \SuperKoderi\Components\ICanonicalUrlFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderBasketFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderStep1FormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderStep2FormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderStep3RegisterFormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IOrderStepSubscriptionFormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IRegisterFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRedirectFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IFilterFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IEntityListFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IPrebasketFactory
			inject: true
		-
			implement: \IVisualPaginatorFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IProductMutationFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IAnimalFilterFactory
			inject: true

		-
			implement: \SuperKoderi\Components\ISubscriptionFormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\ISubscriptionActivationFormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IAdminSubscriptionFormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IAdminSubscriptionOrderFormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IProductToSubscriptionFormFactory
			inject: true

		# Components with true folder matching namespace
		-
			implement: App\AdminModule\Components\SettingsForm\ISettingsFormFactory
			inject: true

		- App\Model\FormThrottler
