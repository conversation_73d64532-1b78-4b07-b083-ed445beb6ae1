<?php

namespace SuperKoderi\Components;


use App\Model\Mutation;
use App\Model\Order;
use App\Model\OrderItem;
use App\Model\Orm;
use App\Model\Orm\BasketItem\BasketItemModel;
use App\Model\VoucherCodeException;
use App\Model\VoucherCodeModel;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\Template;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Http\Session;
use Nette\Utils\ArrayHash;
use SuperKoderi\Basket;
use SuperKoderi\ConfigService;
use SuperKoderi\EasyMessages;
use SuperKoderi\Gtm;
use SuperKoderi\GtmEecAddCouponEvent;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\hasTemplateCacheTrait;
use SuperKoderi\ImageResizer;
use SuperKoderi\IPagesFactory;
use SuperKoderi\Pages;
use SuperKoderi\TranslatorDB;

/**
 * @property Template $template
 */
class OrderBasket extends UI\Control
{
	use hasMessageForFormComponentTrait;
	use hasTemplateCacheTrait;

	private TranslatorDB $translator;

	private ConfigService $configService;

	private Basket $basket;

	private Pages $pages;

	private Mutation $mutation;

	private ImageResizer $imageResizer;

	private Orm $orm;

	private VoucherCodeModel $voucherCodeModel;

	private Request $request;

	private Response $response;

	private BasketItemModel $basketItemModel;

	private EasyMessages $easyMessages;

	private Session $session;

	private Gtm $gtm;

	public function __construct(
		Mutation         $mutation,
		TranslatorDB     $translator,
		ConfigService    $configService,
		Basket           $basket,
		IPagesFactory    $pagesFactory,
		ImageResizer     $imageResizer,
		Orm              $orm,
		VoucherCodeModel $voucherCodeModel,
		Request          $request,
		Response         $response,
		BasketItemModel  $basketItemModel,
		EasyMessages     $easyMessages,
		Session          $session,
		Gtm              $gtm,
	)
	{
		$this->translator = $translator;
		$this->configService = $configService;
		$this->basket = $basket;
		$this->pages = $pagesFactory->create($mutation);
		$this->mutation = $mutation;
		$this->imageResizer = $imageResizer;
		$this->orm = $orm;
		$this->voucherCodeModel = $voucherCodeModel;
		$this->request = $request;
		$this->response = $response;
		$this->basketItemModel = $basketItemModel;
		$this->easyMessages = $easyMessages;
		$this->session = $session;
		$this->gtm = $gtm;
	}


	private function renderInit(): void
	{
		$this->template->ct = $this->getCtFunction();
		$this->template->basket = $this->basket;

		$this->template->isSubscriptionEnabled = $this->isSubcriptionEnabled();

		$this->template->lg = $this->mutation->langCode;
		$this->template->pages = $this->pages;
		$this->template->imageResizer = $this->imageResizer;
		$this->template->setTranslator($this->translator);
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
		$this->template->userEntity = $this->presenter->getUserEntity();
	}


	public function renderAbandoned(Order $abandonedOrder): void
	{
		$this->renderInit();
		$this->template->abandonedOrder = $abandonedOrder;

//		bd($abandonedOrder->productMissing);

		$stdMessage = null;
		if ($abandonedOrder->productMissing) {
			$stdMessage = new \stdClass();
			$stdMessage->text = $this->translator->translate('basket_product_missing');
			$stdMessage->type = 'info';
		}
		$this->template->stdMessage = $stdMessage;

		$this->template->render(__DIR__ . "/abandonedBasket.latte");
	}


	public function render(): void
	{
		$cardErrorMsg = $this->easyMessages->get(EasyMessages::KEY_VOUCHER);
		foreach ($cardErrorMsg as $msg) {
			$this->flashMessage($msg->text, $msg->type);
		}

		$this->renderInit();
		$this->template->order = $this->basket->getFutureOrder();
		$cardErrorMsg = $this->easyMessages->get("rentInfo");
		foreach ($cardErrorMsg as $msg) {
			$this->flashMessage($msg->text, $msg->type);
		}

		// opakovat objednavku - stav
		$repeatState = $this->session->getSection("basketRepeat");
		if ($repeatState->state) {
			$this->flashMessage("repeat_order_status_$repeatState->state", 'info');
		}
		$repeatState->state = null;

		// specialni hlasky pro poukazy
		$cardErrorMsgVoucher = $this->easyMessages->get('voucher');
		$this->template->voucherMsg = $cardErrorMsgVoucher;

		// specialni hlasky pro produkty pridane z kosiku
		$productAddFromBasketMsg = $this->easyMessages->get('basketAdd');
		foreach ($productAddFromBasketMsg as $msg) {
			$this->flashMessage($msg->text, $msg->type);
		}

		$this->template->render(__DIR__ . "/orderBasket.latte");
	}


	public function renderSmall(): void
	{
		$this->renderInit();
		$this->template->order = $this->basket->getFutureOrder();
		$this->template->render(__DIR__ . "/smallBasket.latte");
	}

	/**
	 * @return bool
	 */
	public function isSubcriptionEnabled(): bool
	{
		return $this->mutation->isSubscriptionEnabled || isset($_COOKIE['CLIENT_SUBSCRIPTION_TESTING']);
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setAction($this->presenter->link('UID|basket'));

		$form->setTranslator($this->translator);

		$form->addText('voucherCode', 'voucher');
		$form->addSubmit('recalculate', 'Recalculate');
		$form->addButton('next', 'Next');
		$form->addButton('voucherCheck', 'voucherCheck');
		$form->addButton('continue', 'continue');
		$form->addButton('continue2', 'continue2');
		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}


	public function formError(UI\Form $form): void
	{
//		bd($form->errors);

		if ($this->presenter->isAjax()) {

			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		$changedType = false;

		if (isset($_POST['type'])) {
			// this means that user changed type in basket
			// X is for subscription
			//'X2660' => 'product'
			//'V2485' => 'product'
			//'V2660' => 'product'
			//'V1079-P1078' => product with present
			foreach ($_POST['type'] as $uniqueKey => $type) {
				if (
					(str_starts_with($uniqueKey, 'X') && $type === OrderItem::TYPE_PRODUCT) ||
					(str_starts_with($uniqueKey, 'V') && $type === OrderItem::TYPE_SUBSCRIPTION)
				) {
					if (str_contains($uniqueKey, '-')) {
						// for presents in basket 'V1079-P1078'
						$keys = explode('-', $uniqueKey);
						$amount = $_POST['amount'][$uniqueKey];
						unset($_POST['amount'][$uniqueKey]);
						$this->basket->changeItemKey($uniqueKey, $keys[0]);
						$this->basket->remove($keys[1]);
						$uniqueKey = $keys[0];
						$_POST['amount'][$uniqueKey] = $amount;
						unset($_POST['amount'][$keys[1]]);
					}
					if (str_starts_with($uniqueKey, 'X') && $type === OrderItem::TYPE_PRODUCT) {
						$this->basket->changeSubscriptionProductToOneTime($uniqueKey, (int)$_POST['amount'][$uniqueKey]);
						unset($_POST['amount'][$uniqueKey]);
						$changedType = true;
					}
					if (str_starts_with($uniqueKey, 'V') && $type === OrderItem::TYPE_SUBSCRIPTION) {
						$this->basket->changeProductToSubscription($uniqueKey, (int)$_POST['amount'][$uniqueKey]);
						unset($_POST['amount'][$uniqueKey]);
						$changedType = true;
					}
				}
			}
		}

		if (isset($_POST['amount']) && !$changedType) {
			$amountMsg = [];

			foreach ($_POST['amount'] as $uniqueKey => $amount) {
				$result = $this->basket->setProduct($uniqueKey, (int)$amount);

				if (is_string($result)) {
					$amountMsg[] = $result;
				} elseif ($result === false) {
					$this->flashMessage("cart_items_amount_error", 'error');
				}
			}

			if (!empty($amountMsg)) {
				// $msg = sprintf('%s: %s', $this->translator->translate('cart_items_amount_over_stock'), implode(', ', $amountMsg)); // nelze, vznikaji pak preklady ##cart_items_amount_over_stock: Calibra Dog Life Adult Small Breed Lamb 1,5kg
				$this->flashMessage('cart_items_amount_over_stock', 'info'); // ale zde to nevadi, amount se meni ajaxem = vzdy jeden produkt
			}

			$this->basket->handleFreeSample('edit');

			$this->basket->resetAllPriceDPH();
			$this->basket->recalculateVoucherPrice();

			$msg = $this->basket->validateVouchers(); // dodatecna kontrola voucheru, pokud zmena mnozstvi ovlivni pouziti voucheru - napr. omezeni pri minOrderPrice
			if ($msg !== null) {
				$this->flashMessage('error_voucher_bad_using_common', 'error');
			}
		}

		// sluzby - obsluha
		$actualGift = $this->basket->getSelectedGiftExtra();
		if (isset($_POST['giftExtra'])) {
			$giftId = $_POST['giftExtra'];
			if (isset($actualGift) && $actualGift && isset($actualGift[$giftId])) {
				// edit
				unset($actualGift[$giftId]);
			} else {
				// kontrola zda jej muzu pridat
				$giftExtraCheck = $this->orm->product->getById($giftId);
				if ($giftExtraCheck->isBasketGift && $giftExtraCheck->minPriceOrderBasketGift <= $this->basket->getTotalPriceDPH()) {
					$this->basket->addProductVariant(NULL, 1, ['giftExtra' => $giftId]);
				}
			}
		}
		foreach ($actualGift as $gId => $i) {
			// mazu
			$this->basket->setProduct('G' . $gId, 0);
		}


		// voucher
		if (!empty($valuesAll['voucherCode'])) {
			// overim kod jestli existuje
			$voucherCode = $this->orm->voucherCode->getByCode($valuesAll['voucherCode'], $this->mutation);

			if ($voucherCode) {
				if (!$voucherCode->voucher) {
//					$form->addError("error_voucher_bad");
//					$vMessageError = "error_voucher_bad";
					$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'error_voucher_bad', EasyMessages::TYPE_ERROR);

					$form['voucherCode']->setValue("");
					$this->template->vcodeIsError = TRUE;
				}

				try {
					$isValid = $this->voucherCodeModel->isValid($voucherCode, $this->basket);
					$this->basket->addVoucherCode($voucherCode);
//					$this->flashMessage('voucher_added', 'ok');
					$vMessageOk = 'voucher_added';
					$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'voucher_added', EasyMessages::TYPE_OK);
					if ($this->gtm->isEventEnabled(GtmEecAddCouponEvent::NAME)) {
						$event = (new GtmEecAddCouponEvent($this->gtm))->setup($voucherCode);
						$this->gtm->pushEvent($event);
					}

				} catch (VoucherCodeException $e) {
//					$form->addError($e->getMessage());
					$vMessageError = $e->getMessage();
					$this->easyMessages->send(EasyMessages::KEY_VOUCHER, $vMessageError, EasyMessages::TYPE_ERROR);


					$form['voucherCode']->setValue("");
					$this->template->vcodeIsError = TRUE;
				}
			} else {
				// kod nenalezen
//				$form->addError("error_voucher_bad");
				$vMessageError = 'error_voucher_bad';
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'error_voucher_bad', EasyMessages::TYPE_ERROR);


				$this->template->vcodeIsError = TRUE;
			}

			if (isset($vMessageError) && $vMessageError) {
				$this->template->vc = $valuesAll['voucherCode'];
			}
		}

		if (isset($values['continue2'])) {
//			$this->presenter->redirect('UID|basket');
			// kontrola zda jsou věci k pujcení stále k diposzici
			$test = $this->basket->validateData();
			if ($test !== TRUE) {
//				$this->flashMessage(isset($test['message']) ? $test['message'] : "", 'error');
				$this->easyMessages->send("rentInfo", isset($test['message']) ? $test['message'] : "", EasyMessages::TYPE_ERROR);
				$this->presenter->redirect('UID|basket');
			}

			if (isset($vMessageError) && (bool)$vMessageError) {
//				$this->flashMessage($vMessageError, 'error');
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, $vMessageError, EasyMessages::TYPE_ERROR);

				$this->presenter->redirect('UID|basket');
			}

			if ($this->isSubcriptionEnabled() && $this->basket->containsSubscriptionProducts()) {
				$this->presenter->redirect('UID|stepSubscription');
			} else {
				$this->presenter->redirect('UID|step1');
			}

		} elseif (isset($values['continue'])) {

//			$this->presenter->redirect('UID|basket');
			$this->presenter->redirect('UID|basket');

		} elseif (isset($_POST['voucherCheck']) && $_POST['voucherCheck']) {

//			if (isset($vMessageError) && $vMessageError) {
//				$this->flashMessage($vMessageError, 'error');
//			}
//			if (isset($vMessageOk) && $vMessageOk) {
//				$this->flashMessage($vMessageOk, 'ok');
//			}
			if (isset($vMessageError) && $vMessageError) {
////				$this->flashMessage($vMessageError, 'error');
//
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, $vMessageError, EasyMessages::TYPE_ERROR);
//
			}
//
//			if (isset($vMessageOk) && $vMessageOk) {
////				$this->flashMessage($vMessageOk, 'ok');
//			}

			if ($this->presenter->isAjax() && isset($this->template->vcodeIsError)) { // hack
				bd("1111");
				$this->presenter->redrawControl();
			} else {
				$this->presenter->redirect('UID|basket');
			}


		} else {
			if ($this->presenter->isAjax()) {
				bd("3333");
				$this->presenter->redrawControl();
			} else {
				$this->presenter->redirect('UID|basket');
			}
		}
	}

	public function handleRemove(string $id)
	{
//		$productVariant = $this->orm->productVariant->getActiveVariant($id);
//		if ($productVariant) {
//			$this->basket->remove($productVariant);
//		}
		$this->basket->remove($id);

		$this->basket->handleFreeSample('remove');

		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('basketBig');
			$this->presenter->redrawControl('headerArea');
			$this->presenter->redrawControl('basketHeader');
		} else {
			$this->presenter->redirect('this');
		}
	}


	// ostraneni sluzby, zaruky, skoleni
	public function handleRemoveSub(string $id, string $subKey)
	{
		$this->basket->removeSub($id, $subKey);

		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('basketBig');
			$this->presenter->redrawControl('headerArea');
			$this->presenter->redrawControl('basketHeader');
		} else {
			$this->presenter->redirect('this');
		}
	}


	public function handleRemoveVoucher($voucherId)
	{
		$voucher = $this->orm->voucherCode->getById($voucherId);
		if ($voucher) {
			$this->basket->removeVoucher($voucher);
		}
		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('basketBig');
			$this->presenter->redrawControl('headerArea');
			$this->presenter->redrawControl('basketHeader');
		} else {
			$this->presenter->redirect('this');
		}
	}


	/**
	 * Add 1 amount to variant in basket
	 * @param $id
	 */
	public function handleAdd(string $id)
	{
//		$productVariant = $this->orm->productVariant->getActiveVariant($id);
//		if ($productVariant) {
//			$this->basket->changeProductAmount($productVariant, 1);
//		}
		$this->basket->changeProductAmount($id, 1);

		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}


	public function handleSub(string $id)
	{
//		$productVariant = $this->orm->productVariant->getActiveVariant($id);
//		if ($productVariant) {
//			$this->basket->changeProductAmount($productVariant, -1);
//		}
		$this->basket->changeProductAmount($id, -1);

		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}


	public function handleCleanBasket(): void
	{
		$this->basket->cleanAll();

		if ($this->presenter->isAjax()) {

		} else {
			$this->presenter->redirect('this');
		}
	}


	public function handleCleanAbandonedBasket(): void
	{
		$this->basket->cleanAbandonedBasket();
		$now = new \DateTimeImmutable();
		$this->basket->cleanAll();
		$this->presenter->getHttpResponse()->setCookie('abandonedCounter', "0", $now->add(\DateInterval::createFromDateString('12 months')));
		$this->presenter->redirect('this');
	}


	public function handleSendAbandonedBasket(): void
	{
		$this->basketItemModel->tryFillBasket();
		$now = new \DateTimeImmutable();
		$this->presenter->getHttpResponse()->setCookie('abandonedCounter', "0", $now->add(\DateInterval::createFromDateString('12 months')));
		$this->presenter->redirect('UID|basket');
	}
}


interface IOrderBasketFactory
{
	function create(Mutation $mutation): OrderBasket;
}
