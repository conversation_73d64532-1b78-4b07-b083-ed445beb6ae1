<?php

namespace SuperKoderi\Noviko\Order;


use App\Model;
use App\Model\CreditNoteModel;
use App\Model\OrderModel;
use App\Model\Orm;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use stdClass;
use SuperKoderi\ConfigService;
use SuperKoderi\InvoiceService;
use SuperKoderi\LogicException;
use SuperKoderi\MutationHolder;
use SuperKoderi\Noviko\Product\ConfigLog;
use SuperKoderi\Noviko\WsException;
use SuperKoderi\Noviko\WsService;
use SuperKoderi\OrderMailService;
use SuperKoderi\StringHelper;
use SuperKoderi\Utils\Logger;
use Throwable;
use Tracy;
use function sprintf;

/**
 * Class OrderService
 * @package SuperKoderi\Noviko\Order
 */
class OrderService
{
	use ConfigLog;

	/** @var WsService */
	protected $wsService;

	/** @var Orm */
	protected $orm;

	/** @var OrderMailService */
	protected $orderMailService;

	/** @var string */
	protected $logDir = 'noviko/order';

	/** @var InvoiceService */
	protected $invoiceService;

	/** @var OrderModel */
	protected $orderModel;

	/** @var CreditNoteModel */
	public $creditNoteModel;

	/** @var MutationHolder */
	public $mutationHolder;

	public Model\SubscriptionModel $subscriptionModel;

	public function __construct(ConfigService $configService, WsService $wsService, Orm $orm, OrderMailService $orderMailService, InvoiceService $invoiceService, OrderModel $orderModel, CreditNoteModel $creditNoteModel, MutationHolder $mutationHolder,
		Model\SubscriptionModel $subscriptionModel)
	{
		$this->configService = $configService;
		$this->wsService = $wsService;
		$this->orm = $orm;
		$this->orderMailService = $orderMailService;
		$this->invoiceService = $invoiceService;
		$this->orderModel = $orderModel;
		$this->creditNoteModel = $creditNoteModel;
		$this->mutationHolder = $mutationHolder;
		$this->subscriptionModel = $subscriptionModel;

		$this->initFromConfig();
	}

	/**
	 * Ulozi v Noviku objednavku jako uzavrenou = jde hned na expedici
	 * zmeni stav obj na "Vyrizuje se"
	 * Pro předplatné se mění stav až když má být expedováno a po zaplacení/při dobírce
	 *
	 * @param Model\Order $order
	 * @return Model\Order|IEntity|false
	 */
	public function saveConfirm(Model\Order $order)
	{
		$novikoOrder = null;
		$result = null;

		try {
			$novikoOrder = new Order($order);

			if ($this->configService->isEnvProduction()) {
				$result = $this->wsService->saveObjednavkaHD($novikoOrder, $order->mutation);
				$order = $this->processResult($order, $result);
			} else if ($order->novikoId === null) {
				// local testing
				$order->novikoId = StringHelper::intToNull(rand(100000, 999999));
				$order->novikoHdId = StringHelper::intToNull(rand(100000, 999999));
				$order->novikoStatus = 0;
			}

			$order->novikoUpdated = new DateTimeImmutable();
			if ($order->status !== Model\Order::STATUS_CANCEL) {
				$order->status = Model\Order::STATUS_PROGRESS;
			}
			if ($order->subscriptionOrder instanceof Model\SubscriptionOrder && $order->subscriptionOrder->novikoSubmittedAt === null) {
				$order->subscriptionOrder->novikoSubmittedAt = new DateTimeImmutable();
				$log = new Model\SubscriptionOrderLog();
				$log->message = 'Order exported as open to Noviko';
				$log->type = Model\SubscriptionOrderLog::TYPE_INFO;
				$order->subscriptionOrder->logs->add($log);
			}

			/** @var Model\Order $order */
			$order = $this->orm->persistAndFlush($order);

			$this->processLog($order, $result, __FUNCTION__);
			return $order;

		} catch (Throwable $e) {
			$this->processException($order, $result, __FUNCTION__, $e, $novikoOrder);
		}

		return false;
	}

	/**
	 * @param Model\Order $order
	 * @return bool|array|null
	 *
	 * null         skipped, without Noviko save
	 * true         save ok
	 * array        rejected | error msg
	 *
	 */
	public function saveHeader(Model\Order $order)
	{
		if (!$order->novikoId) {
			$this->logShort([sprintf('NOVIKO %s skipped | Order ID: %d | Unknown order in Noviko', __FUNCTION__, $order->id)], Logger::OK, $order->id);
			return null;
		}

		if (!$order->isCanNovikoSaveHeader) {
			$this->logShort([sprintf('NOVIKO %s rejected | Order ID: %d | Order can no longer be changed', __FUNCTION__, $order->id)], Logger::OK, $order->id);
			return ['order_noviko_msg_save_header_rejected', 'info'];
		}

		$novikoOrder = null;
		$result = null;

		try {
			if ($this->configService->isEnvProduction()) {
				$novikoOrder = new Order($order);
				$result = $this->wsService->updateHlavickaObjednavkaHD($novikoOrder, $order->mutation);
				$order = $this->processResult($order, $result);
			}

			$order->novikoUpdated = new DateTimeImmutable();

			if ($order->subscriptionOrder instanceof Model\SubscriptionOrder && $order->subscriptionOrder->novikoSubmittedAt === null) {
				$order->subscriptionOrder->novikoSubmittedAt = new DateTimeImmutable();
				$log = new Model\SubscriptionOrderLog();
				$log->message = 'Order exported as open to Noviko';
				$log->type = Model\SubscriptionOrderLog::TYPE_INFO;
				$order->subscriptionOrder->logs->add($log);
			}

			/** @var Model\Order $order */
			$order = $this->orm->persistAndFlush($order);

			$this->processLog($order, $result, __FUNCTION__);
			return true;

		} catch (Throwable $e) {
			$this->processException($order, $result, __FUNCTION__, $e, $novikoOrder);
			return ['msg_operation_failed', 'error'];
		}
	}

	/**
	 * @param Model\Order $order
	 * @param string|null $cancelReason
	 * @return array
	 */
	public function storno(Model\Order $order, string $cancelReason)
	{
		$result = null;
		$message = ['order_noviko_msg_storno_failed', 'error'];

		try {
			if ($this->configService->isEnvProduction()) {
				$result = $this->wsService->stornoObjednavkaHD($order->novikoIdObjednavkaPart, $order->mutation);
			} else {
				$result = new \stdClass();
				$result->status = 0;
				$result->message = 'Local environment, no storno';
			}

			if ($result) {

				switch ($result->status) {

					case 0: // OK
					case 6: // obj je v Noviku již stornovaná = stornujeme i v eshopu

						if ($result->status != 6 && in_array($order->novikoStatus, [Order::ID_STATUS_DELAYING_COMPLETE, Order::ID_STATUS_DELAYING_DELIVERY])) { // klient: při stornu ve stavu 14 a 19 nám někdo musí dávat vědět, že stornoval
							$this->orderMailService->orderStornoNovikoWarehouse($order);
						}

						$order->novikoUpdated = new DateTimeImmutable();
						$order->novikoStatus = Order::ID_STATUS_STORNO;

						/** @var Model\Order $order */
						$order = $this->orm->persistAndFlush($order);

						$message = ['order_noviko_msg_storno_ok', 'ok'];
						break;

					default: // ostatní statusy znamenají, že stornovat v Adamovi z nějakého důvodu již nelze

						// puvodne:
						// Noviko sklad: storno email chceme odeslat vzdy 11 az 21
						// ale Adriana: Tá možnosť, že to zachytia, existuje do chvíle, kým nenaskočí status 21 - to je už konečný status, ktorý sa nemení a v tej chvíli z e-shopu posielame aj 2. informačný e-mail s faktúrou.

						if ($order->novikoStatus == Order::ID_STATUS_DELAYING_STOCK) { // klient: Storno nelze ve stavu 11 = error msg a
							$message = ['order_noviko_msg_storno_rejected', 'error'];
						} else { // ostatni stavy -> lze odeslat email storno zadost do skladu
							$this->orderMailService->orderStornoNovikoWarehouse($order);
							$message = ['order_noviko_msg_storno_rejected_request_warehouse', 'error'];
						}

						break;
				}
			}

			$this->processLog($order, $result, __FUNCTION__, true);

		} catch (Throwable $e) {
			$this->processException($order, $result, __FUNCTION__, $e);
		}

		return $message;
	}


	public function fixBrokenOrders(): void
	{
		// FIX function for orders that fail in step3 for some reason

//		$order = $this->orm->order->getById(14212);
//		if ($order->mailConfirmationSend == null) {
//			$this->orm->setMutation($order->mutation);
//			$this->mutationHolder->setMutation($order->mutation);
//			\SuperKoderi\Templating\Helpers::$mutation = $order->mutation;
//			mail("<EMAIL>", "prod calibra cron", $order->id . " " . $order->mutation->langCode . " " . $this->orm->getMutation()->langCode);
//			$this->orderMailService->normalConfirm($order);
//			echo "ok";
//		}
//
//		die;

		if (!$this->configService->isEnvProduction()) {
			$this->logShort('FIX BROKEN ORDERS DISABLED IN NON-PRODUCTION ENVIRONMENT');
			return;
		}

		$orders = $this->orm->order->findBy([
			'status!=' => [Model\Order::STATUS_DONE, Model\Order::STATUS_CANCEL],
			'paymentType' => Model\Order::PAYMENT_ONLINE,
			'paymentStatus' => Model\Order::ONLINE_PAYMENT_PAID,
			'invoiceNumber' => null,
			'novikoId!=' => null,
		]);

		if ($orders->count() > 0) {
			$this->logShort('########################################');
			$this->logShort('FIXING BROKEN ORDERS');

			foreach ($orders as $order) {
				$novikoOrder = $this->wsService->getObjednavkaHD($order->novikoIdObjednavkaPart, $order->mutation);

				if ($novikoOrder == null) {
					$this->logShort('INVALID NOVIKO ORDER STATUS');
					continue;
				}

				$orderStatus = $novikoOrder->getStatus();


				$this->logShort(sprintf('FIXING Order ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $orderStatus), Logger::INFO, $order->id);

				$now = new DateTimeImmutable();

				$order->novikoStatus = $orderStatus;
				$order->novikoUpdated = $now;

				if ($order->invoiceNumber == null) {
					$this->orderModel->doInvoice($order);
					$this->logShort('INVOICE CREATED', Logger::INFO, $order->id);
					$order->invoiceDate = $order->created;
				}

				if ($order->parcelNumber == null) {
					$this->handleParcelNumber($order);
					$this->logShort('PARCEL NUMBER UPDATED', Logger::INFO, $order->id);
				}

				/** @var Model\Order $order */
				$order = $this->orm->persistAndFlush($order);

				if ($order->mailConfirmationSend == null) {
					$this->orm->setMutation($order->mutation);
					$this->mutationHolder->setMutation($order->mutation);
					\SuperKoderi\Templating\Helpers::$mutation = $order->mutation;

					mail("<EMAIL>", "prod calibra cron",$order->id." ".$order->mutation->langCode." ".$this->orm->getMutation()->langCode);

					$this->orderMailService->normalConfirm($order);
					$this->logShort('CONFIRMATION MAIL SENT', Logger::INFO, $order->id);
				}

				$this->logShort('FIXING DONE', Logger::INFO, $order->id);
			}

			$this->logShort('########################################');
		}
	}


	/**
	 * Odesle do Novika vsechny nove objednavky
	 * platba online by mela skoncit se statusem NEW az kdyz je zaplacena
	 */
	public function exportNewOrder()
	{
		$this->log(['START', '############', __METHOD__]);

		$this->fixBrokenOrders();

		$orders = $this->orm->order->findBy([
			'status' => [Model\Order::STATUS_NEW, Model\Order::STATUS_WAITING],
			'novikoId' => null,
		]);

//		// Online payments automatically change status to waiting after payment
//		if ($orders->count() == 0) {
//			$orders = $this->orm->order->findBy([
//				'status' => [Model\Order::STATUS_WAITING],
//				'paymentType' => Model\Order::PAYMENT_ONLINE,
//				'novikoId' => null,
//			]);
//		}

		if ($orders->count() > 0) {
			$counter = $inserted = $errored = 0;
			$this->logShort(['Total orders for export: ' . $orders->count()]);

			foreach ($orders as $order) {
				$counter++;

				// skip non subscription order to export
				if (
					$order->subscriptionOrder === null &&
					$order->status === Model\Order::STATUS_WAITING
				) {
					continue;
				}

				// skip first subscription order to export waiting for payment
				if (
					$order->subscriptionOrder &&
					$order->subscriptionOrder->sequence === 1 &&
					$order->status === Model\Order::STATUS_WAITING
				) {
					continue;
				}

				if ($order->paymentType == Model\Order::PAYMENT_ONLINE && !$order->isPayed && $order->subscriptionOrder == null) {
					$this->logShort(sprintf('SKIPPED Order - NOT PAID YET ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $order->novikoStatus), Logger::INFO, $order->id);
					continue;
				}

				$result = $this->saveConfirm($order);

				if ($result) {
					$inserted++;
					$this->logShort(sprintf('INSERTED Order ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $order->novikoStatus), Logger::OK, $order->id);
				} else {
					$errored++;
					$this->logShort(sprintf('INSERT FAILED Order ID %d status "%s"', $order->id, $order->status), Logger::ERROR, $order->id);
				}
			}
			$this->logShort('########################################');
			$this->logShort(sprintf('TOTAL ITEMS counter: %s, inserted: %s, errored: %s', $counter, $inserted, $errored));
		} else {
			$this->logShort('NO ORDERS TO EXPORT');
		}

		$this->logProfiler();
		$this->log(['END']);
	}

	public function exportSubscriptionOrder()
	{
		$this->log(['START', '############', __METHOD__]);

		$orders = $this->orm->order->findBy([
			ICollection::OR,
			[
				ICollection::AND,
				'status' => [Model\Order::STATUS_NEW, Model\Order::STATUS_WAITING, Model\Order::STATUS_PROGRESS],
				'subscriptionOrder->id!=' => null,
				'subscriptionOrder->orderPaidAt!=' => null,
				'subscriptionOrder->novikoClosedAt=' => null,
				'novikoId!=' => null,
				'novikoStatus' => [Order::ID_STATUS_CREATED, Order::ID_STATUS_OPEN]
			],
			[ //to send immidiately first order from subscription
				ICollection::AND,
				'status' => [Model\Order::STATUS_NEW, Model\Order::STATUS_WAITING],
				'subscriptionOrder->id!=' => null,
				'subscriptionOrder->sequence' => 1,
				'novikoId!=' => null,
				'novikoStatus' => [null, Order::ID_STATUS_CREATED, Order::ID_STATUS_OPEN]
			],
			[ //to cancel skipped subscrtion orders
				ICollection::AND,
				'status' => [Model\Order::STATUS_CANCEL],
				'subscriptionOrder->id!=' => null,
				'novikoId!=' => null,
				'novikoStatus!=' => -10,
				'canceled>' => (new DateTimeImmutable())->modify('-24 hours'),
			],
		]);

		if ($orders->count() > 0) {
			$counter = $inserted = $errored = 0;
			$this->logShort(['Total orders for update: ' . $orders->count()]);

			foreach ($orders as $order) {
				if (!$order->subscriptionOrder instanceof Model\SubscriptionOrder) {
					continue;
				}
				$counter++;

				if ($order->paymentType == Model\Order::PAYMENT_ONLINE && !$order->isPayed && $order->subscriptionOrder == null) {
					$this->logShort(sprintf('SKIPPED Order - NOT PAID YET ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $order->novikoStatus), Logger::INFO, $order->id);
					continue;
				}

				if ($order->readyToCloseInNoviko) {
					if ($order->subscriptionOrder->orderPaidAt !== null) {
						$result = $this->saveConfirm($order);
						if ($order->subscriptionOrder->novikoClosedAt === null) {
							$log = new Model\SubscriptionOrderLog();
							$log->message = 'Order exported as closed to Noviko';
							$log->type = Model\SubscriptionOrderLog::TYPE_INFO;
							$order->subscriptionOrder->logs->add($log);
							$order->subscriptionOrder->novikoClosedAt = new DateTimeImmutable();
							$this->orm->persistAndFlush($order->subscriptionOrder);
						}
					}
				} elseif ($order->status === Model\Order::STATUS_CANCEL) {
					$result = $this->storno($order, '');
					if ($order->subscriptionOrder->novikoClosedAt === null) {
						$log = new Model\SubscriptionOrderLog();
						$log->message = 'Order exported and storno to Noviko';
						$log->type = Model\SubscriptionOrderLog::TYPE_INFO;
						$order->subscriptionOrder->logs->add($log);
						$order->subscriptionOrder->novikoClosedAt = new DateTimeImmutable();
						$this->orm->persistAndFlush($order->subscriptionOrder);
					}
				} else {
					continue;
				}

				if ($result) {
					$inserted++;
					$this->logShort(sprintf('INSERTED Subscription | Order ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $order->novikoStatus), Logger::OK, $order->id);
				} else {
					$errored++;
					$this->logShort(sprintf('INSERT FAILED Subscription | Order ID %d status "%s"', $order->id, $order->status), Logger::ERROR, $order->id);
				}
			}
			$this->logShort('########################################');
			$this->logShort(sprintf('TOTAL ITEMS counter: %s, inserted: %s, errored: %s', $counter, $inserted, $errored));
		} else {
			$this->logShort('NO ORDERS TO EXPORT');
		}

		$this->logProfiler();
		$this->log(['END']);
	}

	/**
	 * Synchronizace stavu vsech nevyrizenych objednavek
	 *
	 * Zmeny stavu podle Noviko statusu:
	 * -----------------------------------------
	 *
	 * Noviko                    Eshop
	 * ***********************************
	 * Predano dopravci    ->    Expedovana
	 * Storno                ->    Stornovana
	 *
	 * Aktualizace polozek
	 * -----------------------------------------
	 * jen do Noviko stavu "Ke kompletaci", pak uz by se polozky nemely menit
	 *
	 */
	public function updateStatusBulk()
	{
		try {
			$this->log(['START', '############', __METHOD__]);

			$orders = $this->orm->order->findBy([
				'novikoId!=' => null,
				'status!=' => [Model\Order::STATUS_DONE, Model\Order::STATUS_CANCEL],
			]);

			if ($orders) {
				$counter = $updated = $skipped = $errored = 0;

				foreach ($orders as $order) {
					$this->mutationHolder->setMutation($order->mutation);
					if (!isset($order->parcelNumber)) {
						$this->handleParcelNumber($order, true);

						if (!isset($order->parcelNumber)) {
							$this->logShort([sprintf('PARCEL NUMBER is NULL even after attempt to update.  ID %d | ORDER NUMBER: %s | Noviko ID %s. SKIPPED FOR THIS RUN', $order->id, $order->number, $order->novikoId)],
							Logger::INFO, $order->id);
							continue;
						}
					}

					$originStatus = $order->status;
					$originNovikoStatus = $order->novikoStatus;
					$counter++;
					$novikoOrder = $this->wsService->getObjednavkaHD($order->novikoIdObjednavkaPart, $order->mutation);

					if (!$novikoOrder) {
						$this->logShort([sprintf('UNKNOWN order NO: %d', $order->novikoIdObjednavkaPart)], Logger::ERROR, $order->id);
						continue;
					}

					try {
						if ($novikoOrder->getStatus() == $order->novikoStatus) { // nic se nedeje
							$skipped++;
							$this->logShort(sprintf('SKIPPED - NO CHANGE Order ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $novikoOrder->getStatus()), Logger::INFO, $order->id);

						} else { // stav se v Noviku zmenil
							$order = $this->changeStatus($order, $novikoOrder);

							$updated++;
							$this->logShort(sprintf('UPDATED Order ID %d status "%s" -> "%s" | Noviko ID %s | Noviko status %s -> %s', $order->id, $originStatus, $order->status, $order->novikoId, $originNovikoStatus, $order->novikoStatus), Logger::OK, $order->id);
						}

					} catch (Throwable $e) {
						$errored++;
						$this->processException($order, null, __FUNCTION__, $e, $novikoOrder);
					}
				}
				$this->logShort('########################################');
				$this->logShort([sprintf('ITEMS counter: %d, updated: %d, skipped: %d, errored: %d', $counter, $updated, $skipped, $errored)]);
			} else {
				$this->logShort('No orders for changeStatusToDispatched');
			}

			$this->logProfiler();
			$this->log(['END']);
		} catch (Throwable $e) {
			$this->log([$e->getMessage()], Logger::ERROR);
			Tracy\Debugger::log($e, Tracy\Debugger::ERROR);
		}
	}

	/**
	 * @param Model\Order $order
	 * @return array
	 */
	public function updateStatus(Model\Order $order)
	{
		$this->log(['USER ACTION', __METHOD__]);
		$message = ['msg_operation_failed', 'error'];
		$originStatus = $order->status;
		$originNovikoStatus = $order->novikoStatus;
		$novikoOrder = null;

		try {
			$novikoOrder = $this->wsService->getObjednavkaHD($order->novikoIdObjednavkaPart, $order->mutation);

			if (!$novikoOrder) {
				return ['order_noviko_msg_unknown_order', 'error'];
			}

			if ($novikoOrder->getStatus() == $order->novikoStatus) { // nic se nedeje
				$this->logShort(sprintf('SKIPPED - NO CHANGE Order ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $novikoOrder->getStatus()), Logger::INFO, $order->id, true);
				return ['order_noviko_msg_update_status_no_changes', 'info'];

			} else { // stav se v Noviku zmenil
				$order = $this->changeStatus($order, $novikoOrder);
				$this->logShort(sprintf('UPDATED Order ID %d status "%s" -> "%s" | Noviko ID %s | Noviko status %s -> %s', $order->id, $originStatus, $order->status, $order->novikoId, $originNovikoStatus, $order->novikoStatus), Logger::OK, $order->id);
				return ['order_noviko_msg_update_status_changed', 'ok'];
			}

		} catch (Throwable $e) {
			$this->processException($order, null, __FUNCTION__, $e, $novikoOrder);
		}
		return $message;
	}

	/**
	 * @throws Throwable
	 * @throws WsException
	 */
	private function changeStatus(Model\Order $order, Order $novikoOrder): Model\Order
	{
		$now = new DateTimeImmutable();
		$order->novikoStatus = $novikoOrder->getStatus(); // always store the noviko status
		$order->novikoUpdated = $now;

		switch ($novikoOrder->getStatus()) {
			case Order::ID_STATUS_STORNO:
				if ($order->hasCreditNote) { // pri uplnem stornu by obj nemala uz mit dobropis
					throw new LogicException(sprintf('Order ID %d already has credit note', $order->id));
				}

				$this->orderModel->storno($order);
				if ($order->subscriptionOrder instanceof Model\SubscriptionOrder) {
					$this->subscriptionModel->cancelSubscriptionOrder($order->subscriptionOrder, Model\SubscriptionOrder::CANCELLING_REASON_NOVIKO_CANCEL);
				}
				$data = $this->creditNoteModel->getDataForLastCreditNote($order, Model\Order::CANCEL_REASON_DEFAULT); // kdyby nahodou obj uz mela nejaky D
				$creditNote = $this->creditNoteModel->create($order, $data);
				/*if ($creditNote) { // gopay refundace
					$this->creditNoteModel->doPaymentRefund($order, $creditNote);
				}*/

				$this->orderMailService->orderStorno($order, $creditNote);

				break;
			case Order::ID_STATUS_FORWARDED_TO_DELIVERY:
				$order->status = Model\Order::STATUS_SHIPPED;

				if ($order->paymentType == Model\Order::PAYMENT_ONDELIVERY) { // u dobirky uz nyni generujeme fakturu, online platba uz fa ma generovanou po zaplaceni
					$this->orderModel->doInvoice($order);
				}

				$this->handleParcelNumber($order);
				$order = $this->orm->persistAndFlush($order); // must be persisted before sending an email
				$this->orderMailService->orderSent($order);

				break;
			case Order::ID_STATUS_RETURNED_BY_THE_CARRIER:
				$order = $this->orm->persistAndFlush($order); // must be persisted before sending an email
				$this->orderMailService->statusChanged($order);

				break;
			default:
				// other statuses, just set the status and do nothing more
				$order = $this->orm->persistAndFlush($order);

				break;
		}

		return $order;
	}


	private function handleItems(Model\Order $order, Order $novikoOrder)
	{
		if (!in_array($novikoOrder->getStatus(),
			[
				Order::ID_STATUS_INCOMPLETE,
				Order::ID_STATUS_CREATED,
				Order::ID_STATUS_OPEN,
				Order::ID_STATUS_DELAYING_COMPLETE,
				Order::ID_STATUS_TO_COMPLETE,
			]
		)) {
			return;
		}
		//dumpe($order->products);
	}

	/**
	 * Prida k objednavce cisla baliku z Novika
	 *
	 * @param Model\Order $order
	 * @throws Throwable
	 * @throws WsException
	 */
	private function handleParcelNumber(Model\Order $order, bool $forcePersist = false): void
	{
		$data = $this->wsService->getObjednavkaOdb($order->novikoIdObjednavkaPart, $order->mutation);

		if ($data) {
			$list = [];
			$list['id'] = isset($data->id) ? $data->id : NULL;
			$list['idZasilky'] = isset($data->idZasilky) ? $data->idZasilky : NULL;

			if (isset($data->baliky)) {
				$list['baliky'] = is_array($data->baliky) ? $data->baliky : [$data->baliky]; // kdyz je v result jen 1 balik, tak <baliky> neni pole, ale stdClass !

				foreach ($list['baliky'] as &$item) {
					$item = (array)$item;
					if (isset($item['idBalikExt']) && $item['idBalikExt']) { // u Heurekapointu je jen 1 balik <idBalikExt>6032166/1</idBalikExt> = je potreba upravit idBalikExt na 6 mistne cislo
						$ext = explode('/', $item['idBalikExt']);
						$item['idBalikExt'] = isset($ext[0]) ? trim($ext[0]) : '';
					}
				}
			} else {
				$list['baliky'] = [];
			}

			$order->parcelNumber = $list;

			if ($forcePersist) {
				$this->orm->persistAndFlush($order);
			}

			$this->logShort([sprintf('SAVE PARCEL NUMBER Order ID: %d', $order->id)], Logger::INFO, $order->id);

			if ($this->verboseLog) {
				$this->logShort(['PARCEL NUMBER', Tracy\Dumper::toText($list)], Logger::INFO, $order->id);
			}
		}
	}

	/**
	 * @param Model\Order $order
	 * @param stdClass|null $result
	 * @return Model\Order|IEntity
	 * @throws NovikoException
	 */
	private function processResult(Model\Order $order, stdClass $result = null)
	{
		if (empty($result) || !isset($result->objednavkaOdb->id)) {
			throw new NovikoException('Noviko not ok result');
		}

		if ($result->objednavkaOdb->idObjednavkaPart != $order->novikoIdObjednavkaPart) {
			throw new NovikoException('idObjednavkaPart doesnt match id in the WS result');
		}

		if (!$order->novikoId) { // obj se do Novika odesila poprve = ulozim si Noviko IDs
			$order->novikoId = StringHelper::intToNull($result->objednavkaOdb->id);
			$order->novikoHdId = StringHelper::intToNull($result->objednavkaOdb->idHDObj);
			$order->novikoStatus = isset($result->status) ? (int)$result->status : null;
		}

		return $order;
	}

	private function processLog(Model\Order $order, $result, string $action, $forceVerboseLog = false)
	{
		$msg = $result instanceof stdClass && isset($result->message) ? $result->message : '';
		$this->logShort([
			sprintf('NOVIKO %s | Order ID: %d Status: %s | IdObjednavkaPart: %d Status: %s | WS message: %s', $action, $order->id, $order->status, $order->novikoIdObjednavkaPart, $order->novikoStatus, $msg)
		], Logger::OK, $order->id);

		if ($this->verboseLog || $forceVerboseLog) {
			$this->logShort(['WS RESULT', Tracy\Dumper::toText($result)], Logger::INFO, $order->id);
		}
	}

	private function processException(Model\Order $order, $result, string $action, Throwable $e, Order $novikoOrder = null)
	{
		$this->logShort([
			sprintf('NOVIKO %s | Order ID: %d | IdObjednavkaPart: %d | %s', $action, $order->id, $order->novikoIdObjednavkaPart, $e->getMessage())
		], Logger::ERROR, $order->id);

		if ($novikoOrder) {
			$this->logShort(['Noviko\Order\Order', Tracy\Dumper::toText($novikoOrder->getData())], Logger::ERROR, $order->id);
		}

		if ($result) {
			$this->logShort(['WS RESULT', Tracy\Dumper::toText($result)], Logger::ERROR, $order->id);
		}

		Tracy\Debugger::log($e, Logger::ERROR);
	}
}

class NovikoException extends LogicException
{

}
