<?php

namespace SuperKoderi;


use App\Model\Mutation;
use App\Model\MutationTransports;
use Nette\Utils\ArrayHash;

class TransportService
{
	// Free delivery promotion configuration
	private const FREE_DELIVERY_PROMO = [
		'start_date' => '2025-05-26 00:00:00',
		'end_date' => '2025-06-02 00:00:00',
		'service_key' => 'ppl_parcel',
		'mutation_id' => Mutation::ID_CS_DEFAULT,
	];

	public function getAll(Mutation $mutation, bool $publicOnly = true, bool $enableTesting = false, float $productsPriceDPH = 0): ArrayHash
	{
		return $this->processTransports($mutation->transportsArray, $mutation->id, $publicOnly, $enableTesting, $productsPriceDPH);
	}

	public function getAllForSubscription(Mutation $mutation, bool $publicOnly = true, bool $enableTesting = false, float $productsPriceDPH = 0): ArrayHash
	{
		return $this->processTransports($mutation->subscriptionTransportsArray, $mutation->id, $publicOnly, $enableTesting, $productsPriceDPH);
	}

	private function processTransports(ArrayHash $transports, int $mutationId, bool $publicOnly, bool $enableTesting, float $productsPriceDPH): ArrayHash
	{
		$retTransports = [];
		$isPromoActive = $this->isFreeDeliveryPromoActive($mutationId);
		$promoServiceKey = null;

		foreach ($transports as $key => $i) {
			if (strpos('personal', $key) !== FALSE) {
				continue;
			}

			if ($publicOnly && $i['isPublic']) {
				$retTransports[$key] = $i;

				// Apply free shipping during promotion period
				if ($isPromoActive && $key === self::FREE_DELIVERY_PROMO['service_key']) {
					$retTransports[$key]['freeFrom'] = 0;
					$retTransports[$key]['freeFromLogIn'] = 0;
					$retTransports[$key]['priceDPH'] = 0;
					$promoServiceKey = $key;
				}

				// REMOVE SUPCAL-682 - changes from 26.5.
				if ($isPromoActive) {
					if ($mutationId == Mutation::ID_CS_DEFAULT) {
						if ($key !== 'ppl_parcel') {
							if ($productsPriceDPH >= $retTransports[$key]['freeFrom'] || $productsPriceDPH >= $retTransports[$key]['freeFromLogIn']) {
								$retTransports[$key]['priceDPH'] = 5;
								$retTransports[$key]['freeFrom'] = 99999;
								$retTransports[$key]['freeFromLogIn'] = 99999;
							}
						}
					}
				}
			}

			if ($enableTesting && $i['isTesting']) {
				$retTransports[$key] = $i;
			}

//			$freeFrom = $i['freeFrom'];
//			if (date('Y-m-d H:i:s') > MutationTransports::BREAK_DATE_FOR_DELIVERY_PRICE) {
//				$freeFrom = MutationTransports::DELIVERY_PRICE_AFTER_BREAKDATE[$mutation->id];
//			}
//			$i['freeFrom'] = $freeFrom;
		}

		// If promotion is active, reorder to put promoted service first
		if ($isPromoActive && $promoServiceKey !== null) {
			$promoService = $retTransports[$promoServiceKey];
			unset($retTransports[$promoServiceKey]);
			$reorderedTransports = [$promoServiceKey => $promoService];
			foreach ($retTransports as $key => $transport) {
				$reorderedTransports[$key] = $transport;
			}
			$retTransports = $reorderedTransports;
		}

		return ArrayHash::from($retTransports);
	}

	static function isFreeDeliveryPromoActive(int $mutationId): bool
	{
		if ($mutationId !== self::FREE_DELIVERY_PROMO['mutation_id']) {
			return false;
		}

		$now = date('Y-m-d H:i:s');
		return $now >= self::FREE_DELIVERY_PROMO['start_date'] && $now < self::FREE_DELIVERY_PROMO['end_date'];
	}
}

