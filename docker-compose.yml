version: '3'

networks:
  calibra:
  superkoders:
    external: true

volumes:
  db:
  es:

services:
  app:
    build:
      dockerfile: docker/app/Dockerfile
      context: .
    hostname: app
    container_name: calibra_app
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - calibra
      - superkoders
    labels:
        - "traefik.enable=true"
        - "traefik.docker.network=superkoders"
        - "traefik.http.routers.calibra.rule=Host(`calibra.superkoders.test`)"
        - "traefik.http.routers.calibra.tls=true"
    volumes:
      - .:/var/www/html
      - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
      - ./docker/elasticsearch/analysis:/usr/share/elasticsearch/config/analysis
    depends_on:
      - db
      - es
      - front
#      - admin

  front:
    build:
      dockerfile: docker/front/Dockerfile
      context: .
    container_name: calibra_front
    restart: unless-stopped
    networks:
      - calibra
    volumes:
      - .:/app

  adminer:
    image: adminer
    restart: unless-stopped
    networks:
      - calibra
    ports:
      - "81:8080"

  db:
    image: mariadb:10
    hostname: calibra_db
    container_name: calibra_db
    restart: unless-stopped
    networks:
      - calibra
    ports:
      - "3306:3306"
    volumes:
        - db:/var/lib/mysql
        - ./docker/db:/docker/db
        - ./docker/db/compare-db.sh:/docker/db/compare-db.sh
    environment:
      MARIADB_HOST: 'calibra_db'
      MARIADB_ROOT_PASSWORD: 'root'
      MARIADB_DATABASE: 'calibra'
      MARIADB_USER: 'calibra'
      MARIADB_PASSWORD: 'calibra'

  db-init:
      image: mariadb:10
      container_name: calibra_db-init
      depends_on:
          - db
      networks:
          - calibra
      volumes:
          - ./docker/db/init-db.sh:/docker/db/init-db.sh
          - ./docker/db/dump.sql:/docker/db/dump.sql
      environment:
          MARIADB_HOST: 'calibra_db'
          MARIADB_ROOT_PASSWORD: 'root'
          MARIADB_DATABASE: 'calibra'
          MARIADB_USER: 'calibra'
          MARIADB_PASSWORD: 'calibra'
      entrypoint: ["/docker/db/init-db.sh"]

  es:
    image: elasticsearch:7.17.6
    hostname: calibra_es
    container_name: calibra_es
    restart: unless-stopped
    networks:
      - calibra
    ports:
      - "9200:9200"
    volumes:
      - es:/usr/share/elasticsearch/data
      - ./docker/elasticsearch/hunspell:/usr/share/elasticsearch/config/hunspell
    environment:
      "discovery.type": single-node

  redis:
      image: redis:latest
      hostname: calibra_redis
      container_name: calibra_redis
      restart: unless-stopped
      networks:
          - calibra
      ports:
          - "6389:6379"

  mailcatcher:
    image: dockage/mailcatcher
    hostname: calibra_mailcatcher
    container_name: calibra_mailcatcher
    restart: unless-stopped
    networks:
      - calibra
    ports:
      - "1080:1080"
